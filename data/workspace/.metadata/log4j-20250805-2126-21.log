2025-08-05 21:26:21,781 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-08-05 21:26:21,783 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-08-05 21:26:21,783 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-08-05 21:26:21,783 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-08-05 21:26:21,783 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-08-05 21:26:21,783 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-05 21:26:21,783 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-08-05 21:26:21,783 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-08-05 21:26:21,784 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-08-05 21:26:21,788 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwentiandeMac-mini-2.local (127.0.0.1)
2025-08-05 21:26:21,793 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-08-05 21:26:21,793 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-08-05 21:26:21,793 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-08-05 21:26:21,793 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-08-05 21:26:21,794 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-08-05 21:26:21,794 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-08-05 21:26:21,799 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-08-05 21:26:21,800 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-08-05 21:26:21,800 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-08-05 21:26:21,800 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-08-05 21:26:21,811 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-08-05 21:26:21,821 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-08-05 21:26:21,822 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-05 21:26:21,822 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-05 21:26:21,828 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 21:26:21,828 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-05 21:26:21,828 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 21:26:21,829 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-05 21:26:21,829 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-05 21:26:21,829 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:21,829 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-05 21:26:21,830 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-08-05 21:26:21,855 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-08-05 21:26:21,856 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-08-05 21:26:21,857 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-08-05 21:26:21,858 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = edd8e131-deda-4726-b43a-5049016be973
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-08-05 21:26:21,859 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-08-05 21:26:21,860 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-08-05 21:26:21,861 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-08-05 21:26:21,863 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-08-05 21:26:21,864 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-08-05 21:26:21,879 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-08-05 21:26:21,881 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - *** License fields ***
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-08-05 21:26:21,883 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-08-05 21:26:21,884 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-08-05 21:26:21,884 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-08-05 21:26:21,884 [main] INFO  PolarionLicensing - *** License fields END ***
2025-08-05 21:26:21,913 [main] INFO  PolarionLicensing - Removing allocations by null
2025-08-05 21:26:21,919 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,919 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,919 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,919 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,919 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,919 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,920 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,921 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,921 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,921 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,921 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,921 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,921 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,921 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-08-05 21:26:21,923 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-08-05 21:26:21,924 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-08-05 21:26:21,924 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-08-05 21:26:22,001 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-08-05 21:26:22,036 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-08-05 21:26:22,037 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-08-05 21:26:22,037 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-08-05 21:26:22,037 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-08-05 21:26:22,037 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-08-05 21:26:22,038 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-08-05 21:26:22,038 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-08-05 21:26:22,038 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-08-05 21:26:22,038 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.checklist
2025-08-05 21:26:22,040 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.checklist to HiveMind
2025-08-05 21:26:22,040 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.test
2025-08-05 21:26:22,044 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.wbs
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.wbs to HiveMind
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasterxml.classmate
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasterxml.jackson
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.jackson.jaxrs
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.woodstox
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.google.gson
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.google.guava
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.google.guava.failureaccess
2025-08-05 21:26:22,045 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.ibm.icu.icu4j
2025-08-05 21:26:22,046 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.icl.saxon
2025-08-05 21:26:22,046 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.jayway.jsonpath.json-path
2025-08-05 21:26:22,046 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.jcraft.jsch
2025-08-05 21:26:22,047 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.networknt.json-schema-validator
2025-08-05 21:26:22,047 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.nimbusds.content-type
2025-08-05 21:26:22,047 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.nimbusds.nimbus-jose-jwt
2025-08-05 21:26:22,047 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.opensymphony.quartz
2025-08-05 21:26:22,047 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.polarion.alm.builder
2025-08-05 21:26:22,048 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-08-05 21:26:22,048 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.polarion.alm.checker
2025-08-05 21:26:22,048 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-08-05 21:26:22,048 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.polarion.alm.extension.vcontext
2025-08-05 21:26:22,048 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-08-05 21:26:22,049 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.polarion.alm.impex
2025-08-05 21:26:22,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-08-05 21:26:22,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.polarion.alm.install
2025-08-05 21:26:22,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.oslc
2025-08-05 21:26:22,052 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-08-05 21:26:22,052 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.projects
2025-08-05 21:26:22,053 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-08-05 21:26:22,053 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.qcentre
2025-08-05 21:26:22,053 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-08-05 21:26:22,053 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.tracker
2025-08-05 21:26:22,053 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-08-05 21:26:22,054 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.ui
2025-08-05 21:26:22,063 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-08-05 21:26:22,064 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.ui.diagrams.mxgraph
2025-08-05 21:26:22,065 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.wiki
2025-08-05 21:26:22,067 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-08-05 21:26:22,067 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.ws
2025-08-05 21:26:22,067 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.ws.client
2025-08-05 21:26:22,068 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.cluster
2025-08-05 21:26:22,068 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-08-05 21:26:22,068 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.core.boot
2025-08-05 21:26:22,069 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.core.util
2025-08-05 21:26:22,069 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.fop
2025-08-05 21:26:22,069 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.platform
2025-08-05 21:26:22,070 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-08-05 21:26:22,070 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.platform.guice
2025-08-05 21:26:22,070 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.platform.hivemind
2025-08-05 21:26:22,070 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-08-05 21:26:22,070 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.platform.jobs
2025-08-05 21:26:22,071 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-08-05 21:26:22,071 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.platform.monitoring
2025-08-05 21:26:22,072 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-08-05 21:26:22,072 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.platform.persistence
2025-08-05 21:26:22,072 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-08-05 21:26:22,072 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform.repository
2025-08-05 21:26:22,072 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-08-05 21:26:22,072 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.repository.driver.svn
2025-08-05 21:26:22,073 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-08-05 21:26:22,073 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.repository.external
2025-08-05 21:26:22,073 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-08-05 21:26:22,073 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.repository.external.git
2025-08-05 21:26:22,073 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-08-05 21:26:22,073 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.repository.external.svn
2025-08-05 21:26:22,074 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-08-05 21:26:22,074 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.sql
2025-08-05 21:26:22,074 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.portal.tomcat
2025-08-05 21:26:22,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.psvn.launcher
2025-08-05 21:26:22,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-08-05 21:26:22,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.psvn.translations.en
2025-08-05 21:26:22,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.purevariants
2025-08-05 21:26:22,082 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-08-05 21:26:22,082 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.qcentre
2025-08-05 21:26:22,083 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.scripting
2025-08-05 21:26:22,089 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-08-05 21:26:22,089 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.scripting.servlet
2025-08-05 21:26:22,089 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.subterra.base
2025-08-05 21:26:22,090 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.subterra.index
2025-08-05 21:26:22,091 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-08-05 21:26:22,091 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.subterra.persistence
2025-08-05 21:26:22,091 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-08-05 21:26:22,091 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.subterra.persistence.document
2025-08-05 21:26:22,092 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-08-05 21:26:22,092 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.synchronizer
2025-08-05 21:26:22,092 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-08-05 21:26:22,092 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.synchronizer.proxy.hpalm
2025-08-05 21:26:22,096 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.synchronizer.proxy.jira
2025-08-05 21:26:22,097 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.synchronizer.proxy.polarion
2025-08-05 21:26:22,097 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.synchronizer.proxy.reqif
2025-08-05 21:26:22,107 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.synchronizer.ui
2025-08-05 21:26:22,107 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.usdp.persistence
2025-08-05 21:26:22,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-08-05 21:26:22,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.xray.doc.user
2025-08-05 21:26:22,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.siemens.des.logger.api
2025-08-05 21:26:22,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.siemens.plm.bitools.analytics
2025-08-05 21:26:22,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.siemens.polarion.ct.collectors.git
2025-08-05 21:26:22,109 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-08-05 21:26:22,109 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.siemens.polarion.eclipse.configurator
2025-08-05 21:26:22,110 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.siemens.polarion.integration.ci
2025-08-05 21:26:22,110 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-08-05 21:26:22,110 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.siemens.polarion.previewer
2025-08-05 21:26:22,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-08-05 21:26:22,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.siemens.polarion.previewer.external
2025-08-05 21:26:22,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-08-05 21:26:22,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.polarion.rest
2025-08-05 21:26:22,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.polarion.rt
2025-08-05 21:26:22,112 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.rt.api
2025-08-05 21:26:22,112 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.rt.collectors.git
2025-08-05 21:26:22,112 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.rt.communication.common
2025-08-05 21:26:22,113 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.rt.communication.polarion
2025-08-05 21:26:22,114 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-08-05 21:26:22,114 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.rt.communication.rt
2025-08-05 21:26:22,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rt.parsers.c
2025-08-05 21:26:22,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt.ui
2025-08-05 21:26:22,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.synchronizer.proxy.tfs
2025-08-05 21:26:22,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.sun.activation.javax.activation
2025-08-05 21:26:22,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.sun.istack.commons-runtime
2025-08-05 21:26:22,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.sun.jna
2025-08-05 21:26:22,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.sun.jna.platform
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.sun.xml.bind.jaxb-impl
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.teamlive.livechecklist
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.livechecklist to HiveMind
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.trilead.ssh2
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.zaxxer.hikariCP
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - des-sdk-core
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - des-sdk-dss
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - io.github.resilience4j.circuitbreaker
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - io.github.resilience4j.core
2025-08-05 21:26:22,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - io.github.resilience4j.retry
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - io.swagger
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - io.vavr
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - jakaroma
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - jakarta.validation.validation-api
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - javassist
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - javax.annotation-api
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - javax.cache
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - javax.el
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - javax.inject
2025-08-05 21:26:22,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - javax.servlet
2025-08-05 21:26:22,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - javax.servlet.jsp
2025-08-05 21:26:22,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - javax.transaction
2025-08-05 21:26:22,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - jaxb-api
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - jcip-annotations
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - jcl.over.slf4j
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - jul.to.slf4j
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - kuromoji-core
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - kuromoji-ipadic
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - lang-tag
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - net.htmlparser.jericho
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - net.java.dev.jna
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - net.minidev.accessors-smart
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - net.minidev.asm
2025-08-05 21:26:22,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - net.minidev.json-smart
2025-08-05 21:26:22,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - net.n3.nanoxml
2025-08-05 21:26:22,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - net.sourceforge.cssparser
2025-08-05 21:26:22,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - nu.xom
2025-08-05 21:26:22,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - oauth2-oidc-sdk
2025-08-05 21:26:22,122 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - org.apache.ant
2025-08-05 21:26:22,130 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - org.apache.avro
2025-08-05 21:26:22,131 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - org.apache.axis
2025-08-05 21:26:22,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - org.apache.batik
2025-08-05 21:26:22,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - org.apache.commons.codec
2025-08-05 21:26:22,136 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - org.apache.commons.collections
2025-08-05 21:26:22,136 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - org.apache.commons.commons-beanutils
2025-08-05 21:26:22,136 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - org.apache.commons.commons-collections4
2025-08-05 21:26:22,136 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.commons.commons-compress
2025-08-05 21:26:22,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.commons.commons-fileupload
2025-08-05 21:26:22,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.commons.digester
2025-08-05 21:26:22,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.commons.exec
2025-08-05 21:26:22,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.io
2025-08-05 21:26:22,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.lang
2025-08-05 21:26:22,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.lang3
2025-08-05 21:26:22,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.logging
2025-08-05 21:26:22,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.curator
2025-08-05 21:26:22,143 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.fop
2025-08-05 21:26:22,144 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.hivemind
2025-08-05 21:26:22,144 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-08-05 21:26:22,145 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.httpcomponents.httpclient
2025-08-05 21:26:22,145 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.httpcomponents.httpcore
2025-08-05 21:26:22,145 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.jasper.glassfish
2025-08-05 21:26:22,145 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.kafka.clients
2025-08-05 21:26:22,146 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.kafka.streams
2025-08-05 21:26:22,146 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.logging.log4j.1.2-api
2025-08-05 21:26:22,146 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.logging.log4j.api
2025-08-05 21:26:22,146 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.logging.log4j.apiconf
2025-08-05 21:26:22,146 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.logging.log4j.core
2025-08-05 21:26:22,147 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.logging.log4j.slf4j-impl
2025-08-05 21:26:22,147 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.lucene.analyzers-common
2025-08-05 21:26:22,147 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.lucene.analyzers-common
2025-08-05 21:26:22,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.lucene.analyzers-smartcn
2025-08-05 21:26:22,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.lucene.core
2025-08-05 21:26:22,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.lucene.core
2025-08-05 21:26:22,150 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.lucene.grouping
2025-08-05 21:26:22,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.lucene.queryparser
2025-08-05 21:26:22,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.oro
2025-08-05 21:26:22,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.pdfbox.fontbox
2025-08-05 21:26:22,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.poi
2025-08-05 21:26:22,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.tika
2025-08-05 21:26:22,700 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.xalan
2025-08-05 21:26:22,700 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.xercesImpl
2025-08-05 21:26:22,700 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.xml.serializer
2025-08-05 21:26:22,700 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.xmlgraphics.commons
2025-08-05 21:26:22,700 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.zookeeper
2025-08-05 21:26:22,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.codehaus.groovy
2025-08-05 21:26:22,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.codehaus.jettison
2025-08-05 21:26:22,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.dom4j
2025-08-05 21:26:22,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.eclipse.core.contenttype
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.eclipse.core.expressions
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.eclipse.core.filesystem
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.eclipse.core.jobs
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.eclipse.core.net
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.eclipse.core.resources
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.eclipse.core.runtime
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.eclipse.equinox.app
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.equinox.common
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.equinox.event
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.equinox.http.registry
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.equinox.http.servlet
2025-08-05 21:26:22,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.equinox.jsp.jasper
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.equinox.jsp.jasper.registry
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.equinox.launcher
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.preferences
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.registry
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.security
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.help
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.help.base
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.help.webapp
2025-08-05 21:26:22,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.jgit
2025-08-05 21:26:22,704 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.osgi.services
2025-08-05 21:26:22,704 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.osgi.util
2025-08-05 21:26:22,704 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.ehcache
2025-08-05 21:26:22,705 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.gitlab.java-gitlab-api
2025-08-05 21:26:22,705 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.glassfish.jersey
2025-08-05 21:26:22,705 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.hibernate.annotations
2025-08-05 21:26:22,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.hibernate.core
2025-08-05 21:26:22,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.hibernate.entitymanager
2025-08-05 21:26:22,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.hibernate.hikaricp
2025-08-05 21:26:22,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.hibernate.jpa.2.1.api
2025-08-05 21:26:22,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.jboss.logging
2025-08-05 21:26:22,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.jvnet.mimepull
2025-08-05 21:26:22,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.objectweb.asm
2025-08-05 21:26:22,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.objectweb.jotm
2025-08-05 21:26:22,707 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.opensaml
2025-08-05 21:26:22,719 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.polarion.svncommons
2025-08-05 21:26:22,719 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.polarion.svnwebclient
2025-08-05 21:26:22,719 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.postgesql
2025-08-05 21:26:22,719 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.projectlombok.lombok
2025-08-05 21:26:22,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.rocksdb.rocksdbjni
2025-08-05 21:26:22,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.springframework.data.core
2025-08-05 21:26:22,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.springframework.data.jpa
2025-08-05 21:26:22,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.springframework.spring-aop
2025-08-05 21:26:22,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.springframework.spring-beans
2025-08-05 21:26:22,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.springframework.spring-context
2025-08-05 21:26:22,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.springframework.spring-core
2025-08-05 21:26:22,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.springframework.spring-expression
2025-08-05 21:26:22,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.springframework.spring-jdbc
2025-08-05 21:26:22,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.spring-orm
2025-08-05 21:26:22,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.spring-test
2025-08-05 21:26:22,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-tx
2025-08-05 21:26:22,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-web
2025-08-05 21:26:22,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-webmvc
2025-08-05 21:26:22,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.tmatesoft.sqljet
2025-08-05 21:26:22,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.tmatesoft.svnkit
2025-08-05 21:26:22,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - saaj-api
2025-08-05 21:26:22,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - sdk-lifecycle-collab
2025-08-05 21:26:22,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - sdk-lifecycle-docmgmt
2025-08-05 21:26:22,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - siemens.des.clientsecurity
2025-08-05 21:26:22,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - slf4j.api
2025-08-05 21:26:22,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - xml-apis
2025-08-05 21:26:22,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - xml.apis.ext
2025-08-05 21:26:22,735 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - xstream
2025-08-05 21:26:22,944 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-08-05 21:26:22,945 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-08-05 21:26:23,166 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-08-05 21:26:23,222 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-08-05 21:26:25,362 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-08-05 21:26:25,689 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-08-05 21:26:25,782 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-05 21:26:25,791 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-08-05 21:26:25,814 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-05 21:26:25,952 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-08-05 21:26:25,994 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-05 21:26:25,998 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-05 21:26:26,000 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *****************************************_history
2025-08-05 21:26:26,062 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 2.89 s. ]
2025-08-05 21:26:26,255 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-08-05 21:26:26,255 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-08-05 21:26:26,380 [main] DEBUG com.fasnote.alm.checklist.ChecklistTemplatesEnumFactory - Creating SingletonProxy for service com.fasnote.alm.checklist.ChecklistTemplatesEnumFactory
2025-08-05 21:26:26,531 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-05 21:26:26,569 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-08-05 21:26:26,652 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-08-05 21:26:26,724 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.192 s. ]
2025-08-05 21:26:26,724 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-05 21:26:26,737 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-08-05 21:26:26,746 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-08-05 21:26:26,789 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0651 s. ]
2025-08-05 21:26:26,789 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-08-05 21:26:26,843 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-08-05 21:26:26,851 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-05 21:26:26,899 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-08-05 21:26:26,968 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-08-05 21:26:26,981 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-08-05 21:26:26,982 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-08-05 21:26:26,992 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-08-05 21:26:27,013 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-08-05 21:26:27,013 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-08-05 21:26:27,101 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [314]
2025-08-05 21:26:27,124 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-08-05 21:26:27,132 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 24 s. ]
2025-08-05 21:26:27,132 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 24 s. ]
2025-08-05 21:26:27,139 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-05 21:26:27,175 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-05 21:26:27,177 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-05 21:26:27,179 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,431.7 GB
 [Tue Aug 05 21:26:27 CST 2025]
2025-08-05 21:26:27,408 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-05 21:26:27,408 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Tue Aug 05 21:26:27 CST 2025]
2025-08-05 21:26:27,413 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-05 21:26:27,413 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Tue Aug 05 21:26:27 CST 2025]
2025-08-05 21:26:27,415 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-05 21:26:27,415 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.6 s. ]
2025-08-05 21:26:27,415 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0584 s [51% update (144x), 49% query (12x)] (221x), svn: 0.0328 s [67% getLatestRevision (2x), 23% testConnection (1x)] (4x)
2025-08-05 21:26:27,416 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-08-05 21:26:27,428 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-08-05 21:26:27,480 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@535708fd
2025-08-05 21:26:27,512 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-08-05 21:26:27,512 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-08-05 21:26:27,512 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-08-05 21:26:27,547 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-08-05 21:26:27,547 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:27,547 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-05 21:26:27,573 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0261 s. ]
2025-08-05 21:26:27,574 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-08-05 21:26:27,574 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.16 s. ]
2025-08-05 21:26:27,574 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0509 s [51% info (3x), 38% getDir2 content (2x)] (6x)
2025-08-05 21:26:27,574 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:27,574 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-05 21:26:27,574 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (6/9) ...
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (1/9) ...
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (5/9) ...
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (3/9) ...
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (2/9) ...
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-05 21:26:27,582 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (4/9) ...
2025-08-05 21:26:27,593 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (6/9) TOOK  [ TIME 0.0111 s. ]
2025-08-05 21:26:27,593 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-05 21:26:27,594 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-08-05 21:26:27,709 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-08-05 21:26:27,719 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-08-05 21:26:27,728 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-08-05 21:26:27,737 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-08-05 21:26:27,738 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-08-05 21:26:27,739 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (3/9) TOOK  [ TIME 0.158 s. ]
2025-08-05 21:26:27,739 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-05 21:26:27,739 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (8/9) ...
2025-08-05 21:26:27,803 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-08-05 21:26:27,811 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-08-05 21:26:27,864 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 288 primary objects (work items+comments).
2025-08-05 21:26:27,865 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (2/9) TOOK  [ TIME 0.284 s. ]
2025-08-05 21:26:27,865 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-05 21:26:27,865 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (9/9) ...
2025-08-05 21:26:27,896 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-08-05 21:26:27,897 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-08-05 21:26:27,897 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (1/9) TOOK  [ TIME 0.316 s. ]
2025-08-05 21:26:27,964 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-08-05 21:26:27,965 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (8/9) TOOK  [ TIME 0.226 s. ]
2025-08-05 21:26:27,982 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-08-05 21:26:28,016 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-08-05 21:26:28,016 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (9/9) TOOK  [ TIME 0.151 s. ]
2025-08-05 21:26:28,031 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-08-05 21:26:28,157 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-08-05 21:26:28,158 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.564 s. ]
2025-08-05 21:26:28,289 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-08-05 21:26:28,289 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (4/9) TOOK  [ TIME 0.707 s. ]
2025-08-05 21:26:28,345 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-08-05 21:26:28,496 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3322 primary objects (work items+comments).
2025-08-05 21:26:28,497 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (5/9) TOOK  [ TIME 0.915 s. ]
2025-08-05 21:26:28,498 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.919 s, CPU [user: 0.286 s, system: 0.417 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.151 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-05 21:26:28,498 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.239 s, system: 0.345 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.201 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-05 21:26:28,499 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.0671 s, system: 0.108 s], Allocated memory: 8.8 MB, transactions: 0, ObjectMaps: 0.0589 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0492 s [80% log2 (5x)] (7x)
2025-08-05 21:26:28,499 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.108 s, system: 0.13 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.147 s [51% log2 (10x), 17% log (1x), 15% info (5x)] (24x), ObjectMaps: 0.0594 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-05 21:26:28,498 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.132 s, system: 0.264 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.126 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0501 s [80% log2 (5x), 11% getLatestRevision (1x)] (7x)
2025-08-05 21:26:28,499 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.0941 s, system: 0.168 s], Allocated memory: 12.4 MB, transactions: 0, svn: 0.085 s [80% log2 (10x)] (13x), ObjectMaps: 0.0847 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-05 21:26:28,499 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.93 s. ]
2025-08-05 21:26:28,499 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.681 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.381 s [66% log2 (36x), 12% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-08-05 21:26:28,517 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-08-05 21:26:28,517 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:28,517 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-05 21:26:28,653 [main | u:p] INFO  TXLOGGER - Tx 661e9a5d6c001_0_661e9a5d6c001_0_: finished. Total: 0.123 s, CPU [user: 0.0935 s, system: 0.00627 s], Allocated memory: 21.8 MB
2025-08-05 21:26:28,671 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_1987a696159.readLocations($IDataHandler_1987a696159.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_1987a695f72.doAsSystemUser($ISecurityService_1987a695f72.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_1987a695f80.addConfigurationListener($IRepositoryConfigService_1987a695f80.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_1987a69603e.initialize($IExternalRepositoryProviderRegistry_1987a69603e.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_1987a69603d.initialize($IExternalRepositoryProviderRegistry_1987a69603d.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_1987a69612d.initModule($IObjectPersistenceModule_1987a69612d.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_1987a696115.initModule($IPersistenceEngine_1987a696115.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_1987a69603a.boot($ILowLevelPersistence_1987a69603a.java) ~[?:?]
	at $ILowLevelPersistence_1987a696039.boot($ILowLevelPersistence_1987a696039.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_1987a695f73.doAsSystemUser($ISecurityService_1987a695f73.java) [?:?]
	at $ISecurityService_1987a695f72.doAsSystemUser($ISecurityService_1987a695f72.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-08-05 21:26:28,816 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-05 21:26:28,831 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-08-05 21:26:28,832 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-08-05 21:26:28,832 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-08-05 21:26:28,832 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.286 s [100% getReadConfiguration (48x)] (48x), svn: 0.11 s [84% info (18x)] (38x)
2025-08-05 21:26:28,862 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:28,862 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-05 21:26:28,862 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-08-05 21:26:28,862 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-08-05 21:26:28,862 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-08-05 21:26:28,863 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-08-05 21:26:28,866 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.00421 s. ]
2025-08-05 21:26:28,866 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-05 21:26:28,866 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-08-05 21:26:28,866 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-08-05 21:26:28,892 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context [global]]
2025-08-05 21:26:28,929 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-08-05 21:26:28,945 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0785 s. ]
2025-08-05 21:26:28,945 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-05 21:26:28,945 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-08-05 21:26:28,945 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-08-05 21:26:28,972 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context WBS]
2025-08-05 21:26:29,012 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-08-05 21:26:29,012 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.0671 s. ]
2025-08-05 21:26:29,012 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-05 21:26:29,012 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-08-05 21:26:29,012 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-08-05 21:26:29,026 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-08-05 21:26:29,035 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-08-05 21:26:29,043 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.0311 s. ]
2025-08-05 21:26:29,043 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-05 21:26:29,043 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-08-05 21:26:29,043 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-08-05 21:26:29,056 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context library]
2025-08-05 21:26:29,073 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-08-05 21:26:29,073 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.03 s. ]
2025-08-05 21:26:29,073 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-05 21:26:29,073 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-08-05 21:26:29,073 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-08-05 21:26:29,101 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context WBSdev]
2025-08-05 21:26:29,129 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-08-05 21:26:29,129 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0556 s. ]
2025-08-05 21:26:29,129 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-05 21:26:29,129 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-08-05 21:26:29,129 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-08-05 21:26:29,147 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context hesai]
2025-08-05 21:26:29,168 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-08-05 21:26:29,168 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.039 s. ]
2025-08-05 21:26:29,168 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-05 21:26:29,168 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-08-05 21:26:29,168 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-08-05 21:26:29,188 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context drivepilot]
2025-08-05 21:26:29,222 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-08-05 21:26:29,222 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.0532 s. ]
2025-08-05 21:26:29,222 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-05 21:26:29,222 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-08-05 21:26:29,222 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-08-05 21:26:29,247 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context elibrary]
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.0639 s. ]
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.424 s. ]
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.45 s. ]
2025-08-05 21:26:29,286 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.347 s [75% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.271 s [100% getReadConfiguration (94x)] (94x)
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-08-05 21:26:29,309 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-08-05 21:26:29,309 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-08-05 21:26:29,309 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0232 s. ]
2025-08-05 21:26:29,309 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-05 21:26:29,335 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:29,335 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-05 21:26:29,336 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-08-05 21:26:29,336 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 21 events
2025-08-05 21:26:29,349 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-08-05 21:26:29,371 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 12 existing objects and 0 deleted objects.
2025-08-05 21:26:29,519 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-05 21:26:29,519 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-08-05 21:26:29,519 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-08-05 21:26:29,519 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-05 21:26:29,543 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-05 21:26:29,544 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-08-05 21:26:29,567 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-08-05 21:26:29,567 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-05 21:26:29,567 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-08-05 21:26:29,567 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-05 21:26:29,567 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-08-05 21:26:29,573 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-05 21:26:29,574 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-08-05 21:26:29,581 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-05 21:26:29,581 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-08-05 21:26:29,583 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-08-05 21:26:29,583 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-08-05 21:26:29,583 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-08-05 21:26:29,590 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-08-05 21:26:29,590 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-08-05 21:26:29,591 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-08-05 21:26:29,592 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-08-05 21:26:29,592 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-08-05 21:26:29,594 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-08-05 21:26:29,596 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-08-05 21:26:29,596 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-08-05 21:26:29,597 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-08-05 21:26:29,597 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-08-05 21:26:29,598 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-08-05 21:26:29,598 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-08-05 21:26:29,600 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-08-05 21:26:29,600 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-05 21:26:29,603 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-05 21:26:29,603 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-08-05 21:26:29,604 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-08-05 21:26:29,608 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-08-05 21:26:29,608 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-08-05 21:26:29,613 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-08-05 21:26:29,613 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-08-05 21:26:29,614 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-08-05 21:26:29,616 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-08-05 21:26:29,616 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-08-05 21:26:29,633 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.127 s, CPU [user: 0.0224 s, system: 0.00598 s], Allocated memory: 8.5 MB, GC: 0.013 s [100% G1 Young Generation (1x)] (1x)
2025-08-05 21:26:29,633 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-08-05 21:26:29,633 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-05 21:26:29,633 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-08-05 21:26:29,634 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-08-05 21:26:29,635 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-05 21:26:29,636 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-08-05 21:26:29,638 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-05 21:26:29,638 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-08-05 21:26:29,638 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-05 21:26:29,638 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-08-05 21:26:29,640 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-08-05 21:26:29,649 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-08-05 21:26:29,649 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}308
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}314
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}309
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}306
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}224
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}225
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}226
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}228
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}230
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}231
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}232
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}233
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}234
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}235
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}239
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}240
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}241
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}242
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}243
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}245
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}246
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}248
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}249
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}250
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}251
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}253
2025-08-05 21:26:29,650 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}254
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}255
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}256
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}257
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}258
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}259
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}260
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}261
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}262
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}263
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}264
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}265
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}267
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}268
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}269
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}270
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}271
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}272
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}273
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}274
2025-08-05 21:26:29,651 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}276
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}277
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}278
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}279
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}280
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}281
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}282
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}283
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}284
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}287
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}288
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}285
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}286
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}290
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}293
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}299
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}300
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}301
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}302
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}294
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}304
2025-08-05 21:26:29,652 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}305
2025-08-05 21:26:29,653 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-05 21:26:29,653 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-08-05 21:26:29,655 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-05 21:26:29,663 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-08-05 21:26:29,664 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.16 s. ]
2025-08-05 21:26:29,691 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-05 21:26:29,693 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-05 21:26:29,693 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-05 21:26:29,790 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-08-05 21:26:29,794 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.458 s. ]
2025-08-05 21:26:29,794 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.48 s. ]
2025-08-05 21:26:29,794 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.458 s [100% doFinishStartup (1x)] (1x), DB: 0.0898 s [57% update (45x), 22% query (20x), 13% execute (15x)] (122x), commit: 0.0588 s [56% Revision (1x), 44% BuildArtifact (1x)] (2x), Lucene: 0.0557 s [100% refresh (2x)] (2x), SubterraURITable: 0.0537 s [100% addIfNotExistsDB (20x)] (20x), resolve: 0.0269 s [76% BuildArtifact (11x), 24% Revision (2x)] (13x)
2025-08-05 21:26:29,795 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-08-05 21:26:29,795 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 2.37 s. ]
2025-08-05 21:26:29,795 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:29,795 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-05 21:26:29,797 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-08-05 21:26:29,805 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-05 21:26:29,805 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:29,805 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-05 21:26:29,830 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-08-05 21:26:29,840 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-05 21:26:29,854 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-05 21:26:29,854 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-05 21:26:29,854 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-05 21:26:29,854 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-05 21:26:29,854 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-08-05 21:26:29,854 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-05 21:26:29,857 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-05 21:26:29,859 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-05 21:26:29,860 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-05 21:26:29,860 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-05 21:26:29,860 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-08-05 21:26:29,870 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-08-05 21:26:30,015 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-08-05 21:26:30,015 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-08-05 21:26:30,027 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-08-05 21:26:30,035 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-08-05 21:26:30,114 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,115 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,115 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,116 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.fasnote.alm.wbs, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,116 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,116 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,116 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,116 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/checklist, contextRoot: src/main/webapp, plugin: com.fasnote.alm.checklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,117 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,117 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,117 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,117 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,117 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,117 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,117 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,117 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/livechecklist, contextRoot: webapp, plugin: com.teamlive.livechecklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,118 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,118 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,118 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,118 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,119 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,119 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,119 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,120 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,120 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,120 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,120 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,120 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,121 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,121 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,121 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,121 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,121 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,121 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,121 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,122 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,122 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,122 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,122 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,122 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,123 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,124 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,124 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,124 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-08-05 21:26:30,185 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-05 21:26:30,194 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 21:26:30,194 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-05 21:26:30,224 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6b4163b5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,224 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@14644607] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,224 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@484a7b4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,224 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5024d48a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,224 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@23aa1d40] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,224 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@22710469] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,244 [Catalina-utility-5] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-08-05 21:26:30,261 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,261 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,262 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,262 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,290 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@668f26a1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,291 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2d6f3ee8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,293 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1bd3906e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,293 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5359e290] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,294 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1c1dbaff] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,298 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,299 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,299 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,303 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3d50f3c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,304 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@749ad467] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,305 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,305 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@28a63d05] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,307 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@69208ca6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,312 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,318 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@33a49aca] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,319 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@27576014] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,319 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6627aa35] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,322 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,325 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,325 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,330 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,333 [Catalina-utility-2] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-05 21:26:30,333 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-05 21:26:30,335 [Catalina-utility-3] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-08-05 21:26:30,338 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@74e8ae5e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,343 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,353 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b899cb7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,359 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,382 [Catalina-utility-4] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-05 21:26:30,407 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2e8022f8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,423 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@733c2020] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,431 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,432 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,440 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@589caa12] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,453 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@578ddb54] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,467 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,476 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@422cb504] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,485 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@11a33021] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,491 [Catalina-utility-2] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-08-05 21:26:30,495 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@207dc625] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,508 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@75cdba58] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,515 [Catalina-utility-3] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-05 21:26:30,515 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@77cc18f7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,527 [Catalina-utility-1] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-08-05 21:26:30,567 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5a235a58] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,576 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,581 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4d4f6efb] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,585 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,593 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@25a7fc5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,605 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,614 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-05 21:26:30,628 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7051f39a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,642 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3725b63] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,648 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,655 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5bb1b018] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,713 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4a269062] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,721 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,727 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@69908483] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,745 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@144e466d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,754 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1b2c8a67] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,758 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,764 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@62446db1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,765 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3fa7cc82] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,767 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,770 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,774 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@38cab2f5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,781 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@333db39a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,785 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-05 21:26:30,790 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-05 21:26:30,792 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7e607638] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:30,927 [Catalina-utility-4] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-05 21:26:31,008 [Catalina-utility-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-08-05 21:26:31,525 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-05 21:26:31,525 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[83]')
2025-08-05 21:26:31,526 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[85]')
2025-08-05 21:26:31,528 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-05 21:26:31,531 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[83]')
2025-08-05 21:26:31,531 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[83]')
2025-08-05 21:26:31,533 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[89]')
2025-08-05 21:26:32,002 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-05 21:26:32,009 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-08-05 21:26:32,010 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-08-05 21:26:32,016 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@24d533a7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-05 21:26:32,022 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-08-05 21:26:32,099 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-08-05 21:26:32,577 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-08-05 21:26:32,640 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-05 21:26:32,757 [ajp-nio-127.0.0.1-8889-exec-1 | cID:7a698624-7f000001-0751ac48-b48bd622] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-05 21:26:32,776 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-05 21:26:32,776 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-08-05 21:26:32,791 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 7a69864e-7f000001-0751ac48-4e1836ae
2025-08-05 21:26:32,791 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-08-05 21:26:32,792 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250805-2126
2025-08-05 21:26:32,793 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-08-05 21:26:32,795 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-08-05 21:26:32,797 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-08-05 21:26:32,798 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Wed Aug 06 01:00:32 CST 2025
2025-08-05 21:26:32,799 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-08-05 21:26:32,799 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-08-05 21:26:32,799 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-08-05 21:26:32,799 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-08-05 21:26:32,799 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-08-05 21:26:32,799 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-08-05 21:26:32,799 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-08-05 21:26:32,799 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-08-05 21:26:32,800 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-08-05 21:26:32,800 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-08-05 21:26:32,801 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-05 21:26:32,801 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-05 21:26:32,807 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 7a698663-7f000001-0751ac48-c0ddf1fe
2025-08-05 21:26:32,807 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-08-05 21:26:32,808 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-08-05 21:26:32,811 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250805-2126_0
2025-08-05 21:26:32,812 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-08-05 21:26:32,812 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-08-05 21:26:32,816 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-08-05 21:26:32,817 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0         26          0%         26         11          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          3          5         38%          8          6          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Tue Aug 05 21:26:32 CST 2025]
2025-08-05 21:26:32,822 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-08-05 21:26:32,824 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-05 21:26:32,824 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.02 s. ]
2025-08-05 21:26:32,825 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.393 s [84% info (158x)] (170x)
2025-08-05 21:26:32,825 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 21:26:32,825 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11 s. ]
2025-08-05 21:26:32,825 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 21:26:32,829 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-08-05 21:26:32,830 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess2811040977256703989.tmp
2025-08-05 21:26:32,833 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-08-05 21:26:32,834 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-08-05 21:26:32,941 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-05 21:26:32,958 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-05 21:26:33,102 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-05 21:26:33,104 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-05 21:26:33,107 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-05 21:26:33,113 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-05 21:26:33,183 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661e9a61a0c45_0_661e9a61a0c45_0_: finished. Total: 0.345 s, CPU [user: 0.174 s, system: 0.0224 s], Allocated memory: 17.4 MB, resolve: 0.113 s [76% User (2x), 21% Project (1x)] (5x), ObjectMaps: 0.0445 s [58% getPrimaryObjectLocation (2x), 35% getPrimaryObjectProperty (2x)] (11x), svn: 0.0273 s [36% getLatestRevision (2x), 34% log (2x), 14% getFile content (2x)] (9x), Lucene: 0.0252 s [100% search (1x)] (1x)
2025-08-05 21:26:33,339 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-08-05 21:26:33,423 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-08-05 21:26:33,725 [Thread-40] INFO  NotificationService - 
2025-08-05 21:26:33,726 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-08-05 21:26:33,726 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-08-05 21:26:33,726 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-08-05 21:26:33,726 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-08-05 21:26:33,726 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-08-05 21:26:33,726 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-08-05 21:26:33,727 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-08-05 21:26:33,727 [Thread-40] INFO  NotificationService - 
2025-08-05 21:26:33,812 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 27, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.871 s. ] 
2025-08-05 21:26:33,818 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.895 s, CPU [user: 0.00581 s, system: 0.00209 s], Allocated memory: 342.3 kB, transactions: 1
2025-08-05 21:26:33,818 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.896 s, CPU [user: 0.00585 s, system: 0.00179 s], Allocated memory: 495.6 kB, transactions: 1
2025-08-05 21:26:33,831 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 73, notification worker: 0.364 s [98% RevisionActivityCreator (18x)] (54x), resolve: 0.132 s [77% User (3x), 18% Project (1x)] (7x), Lucene: 0.0495 s [51% search (1x), 29% add (1x), 20% refresh (2x)] (4x), ObjectMaps: 0.0487 s [61% getPrimaryObjectLocation (3x), 32% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0387 s [100% WorkItem (24x)] (24x), svn: 0.0273 s [36% getLatestRevision (2x), 34% log (2x), 14% getFile content (2x)] (9x), persistence listener: 0.0238 s [79% indexRefreshPersistenceListener (9x), 9% WorkItemActivityCreator (9x)] (63x)
2025-08-05 21:26:33,832 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.01 s, CPU [user: 0.204 s, system: 0.0308 s], Allocated memory: 19.7 MB, transactions: 28, svn: 0.779 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0641 s [73% buildBaselineSnapshots (1x), 27% buildBaseline (27x)] (28x)
2025-08-05 21:26:33,835 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-08-05 21:26:33,837 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-08-05 21:26:33,880 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwentiandeMac-mini-2.local with PID 30430 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess2811040977256703989.tmp)
2025-08-05 21:26:33,885 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-08-05 21:26:34,419 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 27, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.605 s. ] 
2025-08-05 21:26:34,439 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a619fc40_0_661e9a619fc40_0_: finished. Total: 1.61 s, CPU [user: 0.49 s, system: 0.118 s], Allocated memory: 51.6 MB, svn: 1.03 s [56% getDatedRevision (181x), 24% getDir2 content (25x)] (328x), resolve: 0.589 s [100% Category (117x)] (117x), ObjectMaps: 0.217 s [42% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (472x)
2025-08-05 21:26:34,443 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-05 21:26:34,446 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-05 21:26:34,475 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-05 21:26:34,478 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-05 21:26:34,530 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-05 21:26:34,534 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-05 21:26:34,540 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-08-05 21:26:34,548 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-08-05 21:26:34,548 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 21:26:34,549 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-05 21:26:34,603 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-05 21:26:34,603 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 660 ms
2025-08-05 21:26:34,630 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-08-05 21:26:34,694 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a6348470_0_661e9a6348470_0_: finished. Total: 0.165 s, CPU [user: 0.0782 s, system: 0.0128 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0841 s [58% getReadConfiguration (180x), 42% getReadUserConfiguration (10x)] (190x), svn: 0.0816 s [59% info (21x), 36% getFile content (16x)] (39x), resolve: 0.0452 s [100% User (9x)] (9x), ObjectMaps: 0.0174 s [53% getPrimaryObjectProperty (8x), 24% getLastPromoted (8x), 23% getPrimaryObjectLocation (8x)] (32x)
2025-08-05 21:26:34,838 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-08-05 21:26:34,881 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-08-05 21:26:34,881 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-08-05 21:26:34,904 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-08-05 21:26:34,909 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-08-05 21:26:34,910 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-08-05 21:26:34,912 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-08-05 21:26:34,912 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-08-05 21:26:34,924 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-08-05 21:26:34,931 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-08-05 21:26:34,936 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-08-05 21:26:34,936 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-08-05 21:26:34,946 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-08-05 21:26:34,947 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-08-05 21:26:34,950 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-08-05 21:26:34,956 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-08-05 21:26:34,956 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-08-05 21:26:34,956 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-08-05 21:26:34,956 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-08-05 21:26:34,957 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-08-05 21:26:34,958 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-08-05 21:26:34,959 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-08-05 21:26:34,959 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-08-05 21:26:34,959 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-08-05 21:26:34,959 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-08-05 21:26:34,960 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-08-05 21:26:34,960 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-08-05 21:26:34,960 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-08-05 21:26:34,960 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-08-05 21:26:34,963 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-08-05 21:26:34,964 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-08-05 21:26:34,964 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-08-05 21:26:34,964 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-08-05 21:26:34,964 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-08-05 21:26:34,964 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-08-05 21:26:34,964 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-08-05 21:26:34,964 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-08-05 21:26:34,965 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-08-05 21:26:34,965 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - 
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-08-05 21:26:34,981 [Thread-40] INFO  NotificationService - 
2025-08-05 21:26:34,983 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-08-05 21:26:34,988 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.646 seconds (JVM running for 2.135)
2025-08-05 21:26:35,013 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-08-05 21:26:35,022 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-08-05 21:26:35,031 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a6384c72_0_661e9a6384c72_0_: finished. Total: 0.259 s, CPU [user: 0.0883 s, system: 0.00966 s], Allocated memory: 19.7 MB, svn: 0.177 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), RepositoryConfigService: 0.128 s [86% getReadConfiguration (170x)] (192x), GC: 0.015 s [100% G1 Young Generation (1x)] (1x)
2025-08-05 21:26:35,348 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-08-05 21:26:35,356 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-08-05 21:26:35,364 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-08-05 21:26:35,370 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-08-05 21:26:35,377 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-08-05 21:26:35,378 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-08-05 21:26:35,380 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-08-05 21:26:35,381 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-08-05 21:26:35,383 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-08-05 21:26:35,386 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-08-05 21:26:35,395 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-08-05 21:26:35,395 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-08-05 21:26:35,402 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-08-05 21:26:35,418 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-08-05 21:26:35,422 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-08-05 21:26:35,422 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-08-05 21:26:35,423 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-08-05 21:26:35,436 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-08-05 21:26:35,444 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-08-05 21:26:35,448 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-08-05 21:26:35,457 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-08-05 21:26:35,464 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-08-05 21:26:35,473 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-08-05 21:26:35,477 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-08-05 21:26:35,481 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-08-05 21:26:35,484 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-08-05 21:26:35,488 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-08-05 21:26:35,491 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-08-05 21:26:35,494 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-08-05 21:26:35,499 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-08-05 21:26:35,501 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-08-05 21:26:35,504 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-08-05 21:26:35,510 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-08-05 21:26:35,512 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-08-05 21:26:35,517 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-08-05 21:26:35,522 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-08-05 21:26:35,526 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-08-05 21:26:35,530 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-08-05 21:26:35,533 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-08-05 21:26:35,541 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-08-05 21:26:35,544 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-08-05 21:26:35,549 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-08-05 21:26:35,552 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-08-05 21:26:35,555 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-08-05 21:26:35,560 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-08-05 21:26:35,567 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-08-05 21:26:35,571 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-08-05 21:26:35,581 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-08-05 21:26:35,593 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-08-05 21:26:35,602 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-08-05 21:26:35,604 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-08-05 21:26:35,607 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-08-05 21:26:35,611 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-08-05 21:26:35,614 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-08-05 21:26:35,618 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-08-05 21:26:35,627 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-08-05 21:26:35,630 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-08-05 21:26:35,634 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-08-05 21:26:35,637 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-08-05 21:26:35,646 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-08-05 21:26:35,649 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-08-05 21:26:35,654 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-08-05 21:26:35,657 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-08-05 21:26:35,663 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-08-05 21:26:35,667 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-08-05 21:26:35,670 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-08-05 21:26:35,673 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-08-05 21:26:35,677 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-08-05 21:26:35,682 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-08-05 21:26:35,685 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-08-05 21:26:35,689 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-08-05 21:26:35,690 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-08-05 21:26:35,694 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-08-05 21:26:35,698 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-08-05 21:26:35,700 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-08-05 21:26:35,703 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-08-05 21:26:35,716 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-08-05 21:26:35,719 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-08-05 21:26:35,723 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-08-05 21:26:35,727 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-08-05 21:26:35,730 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-08-05 21:26:35,733 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-08-05 21:26:35,738 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-08-05 21:26:35,741 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-08-05 21:26:35,744 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-08-05 21:26:35,751 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-08-05 21:26:35,754 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-08-05 21:26:35,761 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-08-05 21:26:35,764 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-08-05 21:26:35,769 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-08-05 21:26:35,772 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-08-05 21:26:35,775 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-08-05 21:26:35,779 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-08-05 21:26:35,783 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-08-05 21:26:35,788 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-08-05 21:26:35,792 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-08-05 21:26:35,795 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-08-05 21:26:35,800 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-08-05 21:26:35,805 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-08-05 21:26:35,816 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-08-05 21:26:35,820 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-08-05 21:26:35,830 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-08-05 21:26:35,847 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-08-05 21:26:35,851 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-08-05 21:26:35,855 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-08-05 21:26:35,861 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-08-05 21:26:35,864 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-08-05 21:26:35,893 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-08-05 21:26:35,904 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-08-05 21:26:35,909 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-08-05 21:26:35,913 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-08-05 21:26:35,920 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-08-05 21:26:35,925 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-08-05 21:26:35,929 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-08-05 21:26:35,939 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-08-05 21:26:35,944 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-08-05 21:26:35,948 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-08-05 21:26:35,962 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-08-05 21:26:35,969 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-08-05 21:26:35,976 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a63c5c73_0_661e9a63c5c73_0_: finished. Total: 0.945 s, CPU [user: 0.43 s, system: 0.0357 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.715 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.518 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-08-05 21:26:36,169 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-08-05 21:26:36,172 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-08-05 21:26:36,175 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a64b2074_0_661e9a64b2074_0_: finished. Total: 0.198 s, CPU [user: 0.0373 s, system: 0.00527 s], Allocated memory: 17.9 MB, svn: 0.185 s [87% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0316 s [98% getReadConfiguration (124x)] (148x)
2025-08-05 21:26:36,406 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-08-05 21:26:36,409 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-08-05 21:26:36,411 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-08-05 21:26:36,413 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-08-05 21:26:36,415 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-08-05 21:26:36,417 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-08-05 21:26:36,422 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-08-05 21:26:36,424 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-08-05 21:26:36,426 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-08-05 21:26:36,428 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-08-05 21:26:36,430 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-08-05 21:26:36,432 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-08-05 21:26:36,434 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-08-05 21:26:36,435 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-08-05 21:26:36,438 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-08-05 21:26:36,440 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-08-05 21:26:36,445 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-08-05 21:26:36,447 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-08-05 21:26:36,450 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-08-05 21:26:36,451 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-08-05 21:26:36,457 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-08-05 21:26:36,463 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-08-05 21:26:36,466 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-08-05 21:26:36,469 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-08-05 21:26:36,471 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-08-05 21:26:36,473 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-08-05 21:26:36,475 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-08-05 21:26:36,477 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-08-05 21:26:36,483 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-08-05 21:26:36,485 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-08-05 21:26:36,486 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-08-05 21:26:36,489 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-08-05 21:26:36,490 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-08-05 21:26:36,493 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-08-05 21:26:36,495 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-08-05 21:26:36,497 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-08-05 21:26:36,498 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-08-05 21:26:36,501 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-08-05 21:26:36,503 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-08-05 21:26:36,505 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-08-05 21:26:36,508 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-08-05 21:26:36,510 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-08-05 21:26:36,512 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-08-05 21:26:36,517 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-08-05 21:26:36,520 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-08-05 21:26:36,523 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-08-05 21:26:36,526 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-08-05 21:26:36,528 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-08-05 21:26:36,531 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-08-05 21:26:36,534 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-08-05 21:26:36,537 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-08-05 21:26:36,540 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-08-05 21:26:36,542 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-08-05 21:26:36,551 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-08-05 21:26:36,554 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-08-05 21:26:36,557 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-08-05 21:26:36,560 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-08-05 21:26:36,563 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-08-05 21:26:36,565 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-08-05 21:26:36,567 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-08-05 21:26:36,570 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-08-05 21:26:36,573 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-08-05 21:26:36,578 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-08-05 21:26:36,581 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-08-05 21:26:36,587 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-08-05 21:26:36,589 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-08-05 21:26:36,593 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-08-05 21:26:36,596 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-08-05 21:26:36,599 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-08-05 21:26:36,603 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a64f1476_0_661e9a64f1476_0_: finished. Total: 0.374 s, CPU [user: 0.146 s, system: 0.00988 s], Allocated memory: 384.4 MB, RepositoryConfigService: 0.244 s [95% getReadConfiguration (2788x)] (3026x), svn: 0.242 s [55% getFile content (186x), 45% getDir2 content (21x)] (208x)
2025-08-05 21:26:36,671 [PreLoadDataService | u:p] ERROR com.polarion.subterra.base.data.model.CustomField - Unknown custom field type 'enum' - using 'string' - for field with id 'taskType' for 'WorkItem task /default/WBS'
2025-08-05 21:26:36,680 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.checklist.ChecklistTemplatesEnumFactory - Constructing core service implementation for service com.fasnote.alm.checklist.ChecklistTemplatesEnumFactory
2025-08-05 21:26:42,840 [Thread-36] INFO  NotificationService - Notification service was started successfully.
2025-08-05 21:31:32,267 [Thread-32] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer was started successfully.
2025-08-05 21:31:32,963 [Thread-38] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer was started successfully.
2025-08-05 21:36:32,804 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-05 21:36:32,814 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-05 21:36:32,820 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Tue Aug 05 21:36:32 CST 2025]
2025-08-05 21:36:32,843 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-05 21:36:32,843 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,430.6 GB
 [Tue Aug 05 21:36:32 CST 2025]
2025-08-05 21:36:33,038 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-05 21:36:33,038 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Tue Aug 05 21:36:33 CST 2025]
2025-08-05 21:36:33,040 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-05 21:36:33,040 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.138 s.
 [Tue Aug 05 21:36:33 CST 2025]
2025-08-05 21:36:33,042 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-05 21:36:33,043 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Tue Aug 05 21:36:33 CST 2025]
2025-08-05 21:36:33,173 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-05 21:36:33,174 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Tue Aug 05 21:36:33 CST 2025]
2025-08-05 21:36:33,325 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
