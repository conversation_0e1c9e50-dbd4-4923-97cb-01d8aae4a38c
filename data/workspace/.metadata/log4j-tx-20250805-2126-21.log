2025-08-05 21:26:27,415 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0584 s [51% update (144x), 49% query (12x)] (221x), svn: 0.0328 s [67% getLatestRevision (2x), 23% testConnection (1x)] (4x)
2025-08-05 21:26:27,574 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0509 s [51% info (3x), 38% getDir2 content (2x)] (6x)
2025-08-05 21:26:28,498 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.919 s, CPU [user: 0.286 s, system: 0.417 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.151 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-05 21:26:28,498 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.239 s, system: 0.345 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.201 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-05 21:26:28,499 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.0671 s, system: 0.108 s], Allocated memory: 8.8 MB, transactions: 0, ObjectMaps: 0.0589 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0492 s [80% log2 (5x)] (7x)
2025-08-05 21:26:28,499 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.108 s, system: 0.13 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.147 s [51% log2 (10x), 17% log (1x), 15% info (5x)] (24x), ObjectMaps: 0.0594 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-05 21:26:28,499 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.0941 s, system: 0.168 s], Allocated memory: 12.4 MB, transactions: 0, svn: 0.085 s [80% log2 (10x)] (13x), ObjectMaps: 0.0847 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-05 21:26:28,498 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.92 s, CPU [user: 0.132 s, system: 0.264 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.126 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0501 s [80% log2 (5x), 11% getLatestRevision (1x)] (7x)
2025-08-05 21:26:28,499 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.681 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.381 s [66% log2 (36x), 12% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-08-05 21:26:28,653 [main | u:p] INFO  TXLOGGER - Tx 661e9a5d6c001_0_661e9a5d6c001_0_: finished. Total: 0.123 s, CPU [user: 0.0935 s, system: 0.00627 s], Allocated memory: 21.8 MB
2025-08-05 21:26:28,832 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.286 s [100% getReadConfiguration (48x)] (48x), svn: 0.11 s [84% info (18x)] (38x)
2025-08-05 21:26:29,286 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.347 s [75% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.271 s [100% getReadConfiguration (94x)] (94x)
2025-08-05 21:26:29,633 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.127 s, CPU [user: 0.0224 s, system: 0.00598 s], Allocated memory: 8.5 MB, GC: 0.013 s [100% G1 Young Generation (1x)] (1x)
2025-08-05 21:26:29,794 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.458 s [100% doFinishStartup (1x)] (1x), DB: 0.0898 s [57% update (45x), 22% query (20x), 13% execute (15x)] (122x), commit: 0.0588 s [56% Revision (1x), 44% BuildArtifact (1x)] (2x), Lucene: 0.0557 s [100% refresh (2x)] (2x), SubterraURITable: 0.0537 s [100% addIfNotExistsDB (20x)] (20x), resolve: 0.0269 s [76% BuildArtifact (11x), 24% Revision (2x)] (13x)
2025-08-05 21:26:32,825 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.393 s [84% info (158x)] (170x)
2025-08-05 21:26:33,183 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661e9a61a0c45_0_661e9a61a0c45_0_: finished. Total: 0.345 s, CPU [user: 0.174 s, system: 0.0224 s], Allocated memory: 17.4 MB, resolve: 0.113 s [76% User (2x), 21% Project (1x)] (5x), ObjectMaps: 0.0445 s [58% getPrimaryObjectLocation (2x), 35% getPrimaryObjectProperty (2x)] (11x), svn: 0.0273 s [36% getLatestRevision (2x), 34% log (2x), 14% getFile content (2x)] (9x), Lucene: 0.0252 s [100% search (1x)] (1x)
2025-08-05 21:26:33,818 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.895 s, CPU [user: 0.00581 s, system: 0.00209 s], Allocated memory: 342.3 kB, transactions: 1
2025-08-05 21:26:33,818 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.896 s, CPU [user: 0.00585 s, system: 0.00179 s], Allocated memory: 495.6 kB, transactions: 1
2025-08-05 21:26:33,831 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 73, notification worker: 0.364 s [98% RevisionActivityCreator (18x)] (54x), resolve: 0.132 s [77% User (3x), 18% Project (1x)] (7x), Lucene: 0.0495 s [51% search (1x), 29% add (1x), 20% refresh (2x)] (4x), ObjectMaps: 0.0487 s [61% getPrimaryObjectLocation (3x), 32% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0387 s [100% WorkItem (24x)] (24x), svn: 0.0273 s [36% getLatestRevision (2x), 34% log (2x), 14% getFile content (2x)] (9x), persistence listener: 0.0238 s [79% indexRefreshPersistenceListener (9x), 9% WorkItemActivityCreator (9x)] (63x)
2025-08-05 21:26:33,832 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.01 s, CPU [user: 0.204 s, system: 0.0308 s], Allocated memory: 19.7 MB, transactions: 28, svn: 0.779 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0641 s [73% buildBaselineSnapshots (1x), 27% buildBaseline (27x)] (28x)
2025-08-05 21:26:34,439 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a619fc40_0_661e9a619fc40_0_: finished. Total: 1.61 s, CPU [user: 0.49 s, system: 0.118 s], Allocated memory: 51.6 MB, svn: 1.03 s [56% getDatedRevision (181x), 24% getDir2 content (25x)] (328x), resolve: 0.589 s [100% Category (117x)] (117x), ObjectMaps: 0.217 s [42% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (472x)
2025-08-05 21:26:34,694 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a6348470_0_661e9a6348470_0_: finished. Total: 0.165 s, CPU [user: 0.0782 s, system: 0.0128 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0841 s [58% getReadConfiguration (180x), 42% getReadUserConfiguration (10x)] (190x), svn: 0.0816 s [59% info (21x), 36% getFile content (16x)] (39x), resolve: 0.0452 s [100% User (9x)] (9x), ObjectMaps: 0.0174 s [53% getPrimaryObjectProperty (8x), 24% getLastPromoted (8x), 23% getPrimaryObjectLocation (8x)] (32x)
2025-08-05 21:26:35,031 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a6384c72_0_661e9a6384c72_0_: finished. Total: 0.259 s, CPU [user: 0.0883 s, system: 0.00966 s], Allocated memory: 19.7 MB, svn: 0.177 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), RepositoryConfigService: 0.128 s [86% getReadConfiguration (170x)] (192x), GC: 0.015 s [100% G1 Young Generation (1x)] (1x)
2025-08-05 21:26:35,976 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a63c5c73_0_661e9a63c5c73_0_: finished. Total: 0.945 s, CPU [user: 0.43 s, system: 0.0357 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.715 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.518 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-08-05 21:26:36,175 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a64b2074_0_661e9a64b2074_0_: finished. Total: 0.198 s, CPU [user: 0.0373 s, system: 0.00527 s], Allocated memory: 17.9 MB, svn: 0.185 s [87% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0316 s [98% getReadConfiguration (124x)] (148x)
2025-08-05 21:26:36,603 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e9a64f1476_0_661e9a64f1476_0_: finished. Total: 0.374 s, CPU [user: 0.146 s, system: 0.00988 s], Allocated memory: 384.4 MB, RepositoryConfigService: 0.244 s [95% getReadConfiguration (2788x)] (3026x), svn: 0.242 s [55% getFile content (186x), 45% getDir2 content (21x)] (208x)
