2025-08-05 21:26:21,828 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 21:26:21,828 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-05 21:26:21,828 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 21:26:21,829 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-05 21:26:21,829 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-05 21:26:21,829 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:21,829 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-05 21:26:26,531 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-05 21:26:26,724 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.192 s. ]
2025-08-05 21:26:26,724 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-05 21:26:26,789 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0651 s. ]
2025-08-05 21:26:26,851 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-05 21:26:27,132 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 24 s. ]
2025-08-05 21:26:27,415 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.6 s. ]
2025-08-05 21:26:27,547 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:27,547 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-05 21:26:27,574 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.16 s. ]
2025-08-05 21:26:27,574 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:27,574 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-05 21:26:27,581 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-05 21:26:27,593 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-05 21:26:27,739 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-05 21:26:27,865 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-05 21:26:28,499 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.93 s. ]
2025-08-05 21:26:28,517 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:28,517 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-05 21:26:28,816 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-05 21:26:28,832 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-08-05 21:26:28,862 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:28,862 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-05 21:26:28,866 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-05 21:26:28,945 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-05 21:26:29,012 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-05 21:26:29,043 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-05 21:26:29,073 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-05 21:26:29,129 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-05 21:26:29,168 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-05 21:26:29,222 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.45 s. ]
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:29,286 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-05 21:26:29,309 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-05 21:26:29,335 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:29,335 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-05 21:26:29,519 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-05 21:26:29,543 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-05 21:26:29,691 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-05 21:26:29,693 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-05 21:26:29,794 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.48 s. ]
2025-08-05 21:26:29,795 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:29,795 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-05 21:26:29,805 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-05 21:26:29,805 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 21:26:29,805 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-05 21:26:32,824 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.02 s. ]
2025-08-05 21:26:32,825 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 21:26:32,825 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11 s. ]
2025-08-05 21:26:32,825 [main] INFO  com.polarion.platform.startup - ****************************************************************
