2025-08-05 21:19:27 CST  LOG:  starting PostgreSQL 14.18 (Ubuntu 14.18-1.pgdg22.04+1) on aarch64-unknown-linux-gnu, compiled by gcc (Ubuntu 11.4.0-1ubuntu1~22.04) 11.4.0, 64-bit
2025-08-05 21:19:27 CST  LOG:  listening on IPv4 address "127.0.0.1", port 5434
2025-08-05 21:19:27 CST  LOG:  listening on IPv6 address "::1", port 5434
2025-08-05 21:19:27 CST  LOG:  listening on Unix socket "/var/run/postgresql/.s.PGSQL.5434"
2025-08-05 21:19:27 CST  LOG:  database system was interrupted; last known up at 2025-08-05 20:27:09 CST
2025-08-05 21:19:27 CST  LOG:  database system was not properly shut down; automatic recovery in progress
2025-08-05 21:19:27 CST  LOG:  redo starts at 0/F68D138
2025-08-05 21:19:27 CST  LOG:  invalid record length at 0/F68D2A0: wanted 24, got 0
2025-08-05 21:19:27 CST  LOG:  redo done at 0/F68D268 system usage: CPU: user: 0.00 s, system: 0.00 s, elapsed: 0.00 s
2025-08-05 21:19:27 CST  LOG:  database system is ready to accept connections
